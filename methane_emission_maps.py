#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Methane Emission Sources Mapping for Russia and Ukraine
Creates publication-quality maps showing methane emission source locations
"""

import pandas as pd
import matplotlib.pyplot as plt
import cartopy.crs as ccrs
import cartopy.feature as cfeature
from cartopy.mpl.gridliner import LON<PERSON>TUDE_FORMATTER, LATITUDE_FORMATTER
import numpy as np
from matplotlib.patches import Rectangle
import warnings
warnings.filterwarnings('ignore')

# Set matplotlib parameters for publication quality
plt.rcParams.update({
    'font.size': 12,
    'font.family': 'serif',
    'axes.linewidth': 1.2,
    'axes.labelsize': 14,
    'xtick.labelsize': 12,
    'ytick.labelsize': 12,
    'legend.fontsize': 11,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight',
    'savefig.pad_inches': 0.1
})

def read_excel_data(filepath):
    """
    Read Excel file and extract coordinate data
    Assumes latitude is in first column, longitude in second column
    """
    try:
        # Try to read the Excel file
        df = pd.read_excel(filepath, header=0)
        
        # Get the first two columns (assuming lat, lon)
        if df.shape[1] >= 2:
            # Assuming first column is latitude, second is longitude
            coords = df.iloc[:, :2].copy()
            coords.columns = ['latitude', 'longitude']
            
            # Remove any rows with NaN values
            coords = coords.dropna()
            
            # Convert to numeric, coercing errors to NaN
            coords['latitude'] = pd.to_numeric(coords['latitude'], errors='coerce')
            coords['longitude'] = pd.to_numeric(coords['longitude'], errors='coerce')
            
            # Remove any rows that couldn't be converted to numeric
            coords = coords.dropna()
            
            # Basic validation - check if coordinates are within reasonable ranges
            coords = coords[
                (coords['latitude'] >= -90) & (coords['latitude'] <= 90) &
                (coords['longitude'] >= -180) & (coords['longitude'] <= 180)
            ]
            
            return coords
        else:
            print(f"Error: Excel file {filepath} doesn't have at least 2 columns")
            return None
            
    except Exception as e:
        print(f"Error reading {filepath}: {str(e)}")
        return None

def create_methane_map(coords, country_name, extent=None):
    """
    Create a publication-quality map showing methane emission sources
    
    Parameters:
    coords: DataFrame with 'latitude' and 'longitude' columns
    country_name: String name of the country for the title
    extent: List [lon_min, lon_max, lat_min, lat_max] for map bounds
    """
    
    # Create figure and axis with PlateCarree projection
    fig = plt.figure(figsize=(12, 8))
    ax = plt.axes(projection=ccrs.PlateCarree())
    
    # Set map extent based on data if not provided
    if extent is None:
        lon_margin = (coords['longitude'].max() - coords['longitude'].min()) * 0.1
        lat_margin = (coords['latitude'].max() - coords['latitude'].min()) * 0.1
        extent = [
            coords['longitude'].min() - lon_margin,
            coords['longitude'].max() + lon_margin,
            coords['latitude'].min() - lat_margin,
            coords['latitude'].max() + lat_margin
        ]
    
    ax.set_extent(extent, crs=ccrs.PlateCarree())
    
    # Add map features
    ax.add_feature(cfeature.LAND, color='lightgray', alpha=0.8)
    ax.add_feature(cfeature.OCEAN, color='lightblue', alpha=0.6)
    ax.add_feature(cfeature.COASTLINE, linewidth=0.8, color='black')
    ax.add_feature(cfeature.BORDERS, linewidth=1.0, color='black', linestyle='-')
    ax.add_feature(cfeature.RIVERS, linewidth=0.5, color='blue', alpha=0.7)
    ax.add_feature(cfeature.LAKES, color='lightblue', alpha=0.8)
    
    # Add gridlines
    gl = ax.gridlines(crs=ccrs.PlateCarree(), draw_labels=True,
                      linewidth=0.5, color='gray', alpha=0.7, linestyle='--')
    gl.top_labels = False
    gl.right_labels = False
    gl.xlabel_style = {'size': 11}
    gl.ylabel_style = {'size': 11}
    gl.xformatter = LONGITUDE_FORMATTER
    gl.yformatter = LATITUDE_FORMATTER
    
    # Plot methane emission sources
    scatter = ax.scatter(coords['longitude'], coords['latitude'], 
                        c='red', s=25, alpha=0.8, 
                        transform=ccrs.PlateCarree(),
                        edgecolors='darkred', linewidth=0.5,
                        label='Methane Emission Sources')
    
    # Add title
    plt.title(f'Methane Emission Sources in {country_name}', 
              fontsize=16, fontweight='bold', pad=20)
    
    # Add legend
    legend = ax.legend(loc='upper right', frameon=True, fancybox=True, shadow=True)
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_alpha(0.9)
    
    # Add data source note
    plt.figtext(0.02, 0.02, f'Data points: {len(coords)}', 
                fontsize=10, style='italic')
    
    # Add north arrow (simple)
    ax.annotate('N', xy=(0.95, 0.95), xycoords='axes fraction',
                fontsize=14, fontweight='bold', ha='center', va='center',
                bbox=dict(boxstyle='circle', facecolor='white', edgecolor='black'))
    ax.annotate('↑', xy=(0.95, 0.92), xycoords='axes fraction',
                fontsize=12, ha='center', va='center')
    
    plt.tight_layout()
    return fig, ax

def main():
    """Main function to process both countries and create maps"""
    
    # File paths
    russia_file = "俄罗斯7.27.xlsx"
    ukraine_file = "乌克兰.xlsx"
    
    # Process Russia data
    print("Processing Russia methane emission data...")
    russia_coords = read_excel_data(russia_file)
    
    if russia_coords is not None and len(russia_coords) > 0:
        print(f"Found {len(russia_coords)} valid coordinate pairs for Russia")
        
        # Create Russia map
        fig_russia, ax_russia = create_methane_map(russia_coords, "Russia")
        
        # Save Russia map
        plt.savefig('Russia_Methane_Emissions.png', dpi=300, bbox_inches='tight')
        plt.savefig('Russia_Methane_Emissions.pdf', dpi=300, bbox_inches='tight')
        print("Russia map saved as 'Russia_Methane_Emissions.png' and 'Russia_Methane_Emissions.pdf'")
        plt.show()
        
    else:
        print("No valid data found for Russia")
    
    # Process Ukraine data
    print("\nProcessing Ukraine methane emission data...")
    ukraine_coords = read_excel_data(ukraine_file)
    
    if ukraine_coords is not None and len(ukraine_coords) > 0:
        print(f"Found {len(ukraine_coords)} valid coordinate pairs for Ukraine")
        
        # Create Ukraine map
        fig_ukraine, ax_ukraine = create_methane_map(ukraine_coords, "Ukraine")
        
        # Save Ukraine map
        plt.savefig('Ukraine_Methane_Emissions.png', dpi=300, bbox_inches='tight')
        plt.savefig('Ukraine_Methane_Emissions.pdf', dpi=300, bbox_inches='tight')
        print("Ukraine map saved as 'Ukraine_Methane_Emissions.png' and 'Ukraine_Methane_Emissions.pdf'")
        plt.show()
        
    else:
        print("No valid data found for Ukraine")

if __name__ == "__main__":
    main()
